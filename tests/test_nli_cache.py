"""
NLI缓存系统测试
"""

import unittest
import tempfile
import os
import pandas as pd
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.nli_calculator import NLICalculator, CachedNLICalculator, NLIResult


class TestNLICalculator(unittest.TestCase):
    """NLI计算器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_texts = [
            "The weather is sunny today.",
            "It's a bright day.",
            "The sky is cloudy.",
            "Rain is falling."
        ]
    
    @patch('core.nli_calculator.AutoTokenizer')
    @patch('core.nli_calculator.AutoModelForSequenceClassification')
    @patch('torch.cuda.is_available')
    def test_nli_calculator_init(self, mock_cuda, mock_model, mock_tokenizer):
        """测试NLI计算器初始化"""
        mock_cuda.return_value = False
        mock_tokenizer.from_pretrained.return_value = MagicMock()
        mock_model.from_pretrained.return_value = MagicMock()
        
        calculator = NLICalculator("test-model", verbose=True)
        
        self.assertEqual(calculator.model_name, "test-model")
        self.assertTrue(calculator.verbose)
        mock_tokenizer.from_pretrained.assert_called_once_with("test-model")
        mock_model.from_pretrained.assert_called_once_with("test-model")
    
    @patch('core.nli_calculator.torch')
    def test_compute_nli_scores_success(self, mock_torch):
        """测试成功计算NLI分数"""
        # 模拟torch相关操作
        mock_torch.no_grad.return_value.__enter__ = MagicMock()
        mock_torch.no_grad.return_value.__exit__ = MagicMock()
        mock_torch.softmax.return_value.cpu.return_value.numpy.return_value = [[0.1, 0.2, 0.7]]
        
        # 创建模拟的计算器
        calculator = NLICalculator.__new__(NLICalculator)
        calculator.model_name = "test-model"
        calculator.verbose = False
        calculator.tokenizer = MagicMock()
        calculator.model = MagicMock()
        calculator.device = "cpu"
        
        # 模拟tokenizer和model的返回值
        calculator.tokenizer.return_value.to.return_value = MagicMock()
        calculator.model.return_value.logits = MagicMock()
        
        result = calculator.compute_nli_scores("text1", "text2")
        
        self.assertIsInstance(result, NLIResult)
        self.assertEqual(result.contradiction, 0.1)
        self.assertEqual(result.neutral, 0.2)
        self.assertEqual(result.entailment, 0.7)
    
    def test_compute_nli_scores_error_handling(self):
        """测试NLI分数计算错误处理"""
        # 创建一个会抛出异常的计算器
        calculator = NLICalculator.__new__(NLICalculator)
        calculator.model_name = "test-model"
        calculator.verbose = False
        calculator.tokenizer = MagicMock(side_effect=Exception("Test error"))
        calculator.model = MagicMock()
        calculator.device = "cpu"
        
        result = calculator.compute_nli_scores("text1", "text2")
        
        # 应该返回默认的均匀分布
        self.assertIsInstance(result, NLIResult)
        self.assertAlmostEqual(result.entailment, 0.33, places=2)
        self.assertAlmostEqual(result.neutral, 0.34, places=2)
        self.assertAlmostEqual(result.contradiction, 0.33, places=2)
    
    def test_compute_entailment_score(self):
        """测试计算entailment分数（向后兼容）"""
        calculator = NLICalculator.__new__(NLICalculator)
        calculator.compute_nli_scores = MagicMock(return_value=NLIResult(0.8, 0.1, 0.1))
        
        score = calculator.compute_entailment_score("text1", "text2")
        self.assertEqual(score, 0.8)
    
    def test_get_text_hash(self):
        """测试文本哈希生成"""
        hash1 = NLICalculator.get_text_hash("test text")
        hash2 = NLICalculator.get_text_hash("test text")
        hash3 = NLICalculator.get_text_hash("different text")
        
        self.assertEqual(hash1, hash2)  # 相同文本应该有相同哈希
        self.assertNotEqual(hash1, hash3)  # 不同文本应该有不同哈希
        self.assertEqual(len(hash1), 32)  # MD5哈希长度应该是32


class TestCachedNLICalculator(unittest.TestCase):
    """缓存NLI计算器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.calculator = CachedNLICalculator.__new__(CachedNLICalculator)
        self.calculator.model_name = "test-model"
        self.calculator.verbose = False
        self.calculator.nli_cache = {}
        self.calculator.get_cache_key = lambda text1, text2: f"{text1}||{text2}||test-model"
    
    def test_compute_nli_scores_cached_hit(self):
        """测试缓存命中"""
        # 预设缓存
        cache_key = "text1||text2||test-model"
        cached_result = NLIResult(0.6, 0.3, 0.1)
        self.calculator.nli_cache[cache_key] = cached_result
        
        result = self.calculator.compute_nli_scores_cached("text1", "text2")
        
        self.assertEqual(result, cached_result)
    
    def test_compute_nli_scores_cached_miss(self):
        """测试缓存未命中"""
        # 模拟compute_nli_scores方法
        expected_result = NLIResult(0.7, 0.2, 0.1)
        self.calculator.compute_nli_scores = MagicMock(return_value=expected_result)
        
        result = self.calculator.compute_nli_scores_cached("text1", "text2")
        
        self.assertEqual(result, expected_result)
        # 验证结果被缓存
        cache_key = "text1||text2||test-model"
        self.assertIn(cache_key, self.calculator.nli_cache)
        self.assertEqual(self.calculator.nli_cache[cache_key], expected_result)
    
    def test_get_cache_stats(self):
        """测试获取缓存统计"""
        self.calculator.nli_cache = {"key1": "value1", "key2": "value2"}
        
        stats = self.calculator.get_cache_stats()
        
        self.assertEqual(stats['total_entries'], 2)
        self.assertEqual(stats['model_name'], "test-model")
    
    def test_clear_cache(self):
        """测试清空缓存"""
        self.calculator.nli_cache = {"key1": "value1", "key2": "value2"}
        
        self.calculator.clear_cache()
        
        self.assertEqual(len(self.calculator.nli_cache), 0)


class TestNLICacheIntegration(unittest.TestCase):
    """NLI缓存集成测试"""
    
    def test_csv_cache_operations(self):
        """测试CSV缓存操作"""
        # 这个测试需要模拟analyze_all_uq_methods中的缓存操作
        test_cache_data = {
            "hash1||hash2||model": {
                'text1': 'Hello world',
                'text2': 'Hi there',
                'model_name': 'test-model',
                'entailment': 0.8,
                'neutral': 0.1,
                'contradiction': 0.1,
                'text1_hash': 'hash1',
                'text2_hash': 'hash2',
                'timestamp': '2025-07-31 12:00:00'
            }
        }
        
        # 创建临时CSV文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df = pd.DataFrame([{
                'text1': 'Hello world',
                'text2': 'Hi there',
                'model_name': 'test-model',
                'entailment': 0.8,
                'neutral': 0.1,
                'contradiction': 0.1,
                'text1_hash': 'hash1',
                'text2_hash': 'hash2',
                'timestamp': '2025-07-31 12:00:00'
            }])
            df.to_csv(f.name, index=False)
            csv_file = f.name
        
        try:
            # 读取CSV文件
            df_loaded = pd.read_csv(csv_file)
            
            # 验证数据完整性
            self.assertEqual(len(df_loaded), 1)
            self.assertEqual(df_loaded.iloc[0]['text1'], 'Hello world')
            self.assertEqual(df_loaded.iloc[0]['entailment'], 0.8)
            
        finally:
            os.unlink(csv_file)
    
    def test_cache_key_generation(self):
        """测试缓存键生成"""
        calculator = NLICalculator("test-model")
        
        key1 = calculator.get_cache_key("text1", "text2")
        key2 = calculator.get_cache_key("text1", "text2")
        key3 = calculator.get_cache_key("text2", "text1")
        
        self.assertEqual(key1, key2)  # 相同输入应该生成相同键
        self.assertNotEqual(key1, key3)  # 不同输入应该生成不同键
    
    def test_similarity_matrix_computation(self):
        """测试相似度矩阵计算"""
        calculator = CachedNLICalculator.__new__(CachedNLICalculator)
        calculator.compute_nli_scores_cached = MagicMock()
        
        # 模拟NLI分数计算结果
        def mock_nli_scores(text1, text2):
            if text1 == text2:
                return NLIResult(1.0, 0.0, 0.0)
            else:
                return NLIResult(0.5, 0.3, 0.2)
        
        calculator.compute_nli_scores_cached.side_effect = mock_nli_scores
        
        responses = ["text1", "text2", "text3"]
        matrix = calculator.compute_similarity_matrix_cached(responses)
        
        # 验证矩阵形状和对角线
        self.assertEqual(matrix.shape, (3, 3))
        for i in range(3):
            self.assertEqual(matrix[i, i], 1.0)  # 对角线应该是1.0
        
        # 验证矩阵对称性
        for i in range(3):
            for j in range(3):
                self.assertAlmostEqual(matrix[i, j], matrix[j, i], places=5)


class TestNLIResultClass(unittest.TestCase):
    """NLIResult类测试"""
    
    def test_nli_result_creation(self):
        """测试NLIResult创建"""
        result = NLIResult(0.7, 0.2, 0.1)
        
        self.assertEqual(result.entailment, 0.7)
        self.assertEqual(result.neutral, 0.2)
        self.assertEqual(result.contradiction, 0.1)
    
    def test_nli_result_equality(self):
        """测试NLIResult相等性"""
        result1 = NLIResult(0.7, 0.2, 0.1)
        result2 = NLIResult(0.7, 0.2, 0.1)
        result3 = NLIResult(0.6, 0.3, 0.1)
        
        self.assertEqual(result1, result2)
        self.assertNotEqual(result1, result3)
    
    def test_nli_result_sum_validation(self):
        """测试NLI分数总和验证"""
        result = NLIResult(0.5, 0.3, 0.2)
        total = result.entailment + result.neutral + result.contradiction
        
        # 总和应该接近1.0
        self.assertAlmostEqual(total, 1.0, places=5)


if __name__ == '__main__':
    unittest.main()
