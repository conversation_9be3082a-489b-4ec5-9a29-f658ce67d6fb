"""
配置管理器测试
"""

import unittest
import tempfile
import os
import yaml
from unittest.mock import patch, mock_open

# 添加项目根目录到路径
import sys
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core.config_manager import Config<PERSON>anager, get_config_manager, reload_config


class TestConfigManager(unittest.TestCase):
    """配置管理器测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.test_config = {
            'data': {
                'twitter_csv': 'test_twitter.csv',
                'commit_csv': 'test_commit.csv',
                'output_dir': 'test_output',
                'sample_sizes': {
                    'twitter': [5, 10],
                    'commit': [5, 10, 15]
                }
            },
            'nli': {
                'default_model': 'test-model',
                'cache': {
                    'cache_save_interval': 3
                }
            },
            'performance': {
                'memory_threshold_mb': 2000
            }
        }
    
    def test_load_config_from_file(self):
        """测试从文件加载配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(self.test_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            self.assertEqual(config_manager.get('data.twitter_csv'), 'test_twitter.csv')
            self.assertEqual(config_manager.get('nli.default_model'), 'test-model')
        finally:
            os.unlink(config_file)
    
    def test_load_config_file_not_found(self):
        """测试配置文件不存在时的处理"""
        config_manager = ConfigManager('nonexistent_config.yaml')
        # 应该使用默认配置
        self.assertIsNotNone(config_manager.get('data.twitter_csv'))
        self.assertIsNotNone(config_manager.get('nli.default_model'))
    
    def test_get_nested_config(self):
        """测试获取嵌套配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(self.test_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            self.assertEqual(config_manager.get('data.sample_sizes.twitter'), [5, 10])
            self.assertEqual(config_manager.get('nli.cache.cache_save_interval'), 3)
            self.assertEqual(config_manager.get('nonexistent.key', 'default'), 'default')
        finally:
            os.unlink(config_file)
    
    def test_set_config(self):
        """测试设置配置值"""
        config_manager = ConfigManager('nonexistent_config.yaml')
        config_manager.set('test.nested.value', 'test_value')
        self.assertEqual(config_manager.get('test.nested.value'), 'test_value')
    
    def test_get_sample_sizes(self):
        """测试获取样本大小配置"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(self.test_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            self.assertEqual(config_manager.get_sample_sizes('twitter'), [5, 10])
            self.assertEqual(config_manager.get_sample_sizes('commit'), [5, 10, 15])
            self.assertEqual(config_manager.get_sample_sizes('unknown'), [10, 15, 20])  # 默认值
        finally:
            os.unlink(config_file)
    
    def test_get_memory_threshold(self):
        """测试获取内存阈值"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(self.test_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            self.assertEqual(config_manager.get_memory_threshold(), 2000)
        finally:
            os.unlink(config_file)
    
    def test_validate_config_valid(self):
        """测试有效配置的验证"""
        # 创建包含所有必需字段的配置
        valid_config = {
            'data': {
                'twitter_csv': 'data/all_twitter_responses.csv',
                'commit_csv': 'data/all_commit_responses.csv',
                'output_dir': 'data/output',
                'sample_sizes': {
                    'twitter': [10, 15, 20],
                    'commit': [10, 15, 20, 25, 30]
                }
            },
            'nli': {
                'default_model': 'microsoft/deberta-large-mnli'
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(valid_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            # 由于文件可能不存在，验证可能失败，但不应该抛出异常
            result = config_manager.validate_config()
            self.assertIsInstance(result, bool)
        finally:
            os.unlink(config_file)
    
    def test_validate_config_missing_required(self):
        """测试缺少必需字段时的配置验证"""
        invalid_config = {
            'data': {
                'twitter_csv': 'test.csv'
                # 缺少其他必需字段
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(invalid_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            result = config_manager.validate_config()
            self.assertFalse(result)
        finally:
            os.unlink(config_file)
    
    def test_save_config(self):
        """测试保存配置"""
        config_manager = ConfigManager('nonexistent_config.yaml')
        config_manager.set('test.value', 'saved_value')
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            output_file = f.name
        
        try:
            config_manager.save_config(output_file)
            
            # 验证保存的配置
            new_config_manager = ConfigManager(output_file)
            self.assertEqual(new_config_manager.get('test.value'), 'saved_value')
        finally:
            if os.path.exists(output_file):
                os.unlink(output_file)
    
    def test_singleton_config_manager(self):
        """测试单例模式的配置管理器"""
        manager1 = get_config_manager()
        manager2 = get_config_manager()
        self.assertIs(manager1, manager2)
    
    def test_reload_config(self):
        """测试重新加载配置"""
        # 这个测试主要确保reload_config函数不会抛出异常
        try:
            reload_config('nonexistent_config.yaml')
            manager = get_config_manager()
            self.assertIsNotNone(manager)
        except Exception as e:
            self.fail(f"reload_config raised an exception: {e}")


class TestConfigManagerIntegration(unittest.TestCase):
    """配置管理器集成测试"""
    
    def test_config_manager_with_real_structure(self):
        """测试配置管理器与真实配置结构的集成"""
        # 模拟真实的配置结构
        real_config = {
            'data': {
                'twitter_csv': 'data/all_twitter_responses.csv',
                'commit_csv': 'data/all_commit_responses.csv',
                'output_dir': 'data/Uq_Evaluation_20250731',
                'cache_dir': 'cache',
                'sample_sizes': {
                    'twitter': [10, 15, 20],
                    'commit': [10, 15, 20, 25, 30]
                },
                'validation': {
                    'required_columns': {
                        'twitter': ['tweet_index', 'prompt_type', 'response_text'],
                        'commit': ['commit_sha', 'prompt_type', 'response_text']
                    }
                }
            },
            'nli': {
                'default_model': 'microsoft/deberta-large-mnli',
                'available_models': [
                    'microsoft/deberta-large-mnli',
                    'cross-encoder/nli-deberta-v3-base'
                ],
                'cache': {
                    'enable_csv_cache': True,
                    'cache_save_interval': 5
                }
            },
            'performance': {
                'memory_monitoring': True,
                'memory_threshold_mb': 4000,
                'batch_processing': {
                    'intermediate_save_interval': 20
                }
            },
            'error_handling': {
                'continue_on_error': True,
                'save_error_results': True
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(real_config, f)
            config_file = f.name
        
        try:
            config_manager = ConfigManager(config_file)
            
            # 测试各种配置获取方法
            self.assertEqual(config_manager.get_sample_sizes('twitter'), [10, 15, 20])
            self.assertEqual(config_manager.get_memory_threshold(), 4000)
            self.assertEqual(config_manager.get_cache_save_interval(), 5)
            self.assertEqual(config_manager.get_intermediate_save_interval(), 20)
            self.assertTrue(config_manager.is_memory_monitoring_enabled())
            self.assertTrue(config_manager.should_continue_on_error())
            self.assertTrue(config_manager.should_save_error_results())
            
            # 测试配置分组获取
            data_config = config_manager.get_data_config()
            self.assertIn('twitter_csv', data_config)
            self.assertIn('sample_sizes', data_config)
            
            nli_config = config_manager.get_nli_config()
            self.assertIn('default_model', nli_config)
            self.assertIn('cache', nli_config)
            
        finally:
            os.unlink(config_file)


if __name__ == '__main__':
    unittest.main()
